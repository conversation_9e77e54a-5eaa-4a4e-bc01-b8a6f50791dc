

<?php $__env->startSection('title', __('churches.church_details')); ?>
<?php $__env->startSection('page-title', __('churches.church_details')); ?>

<?php $__env->startSection('breadcrumbs'); ?>
    <li>
        <span class="mx-2">/</span>
        <a href="<?php echo e(route('churches.index')); ?>" class="hover:text-gray-700"><?php echo e(__('churches.churches')); ?></a>
    </li>
    <li>
        <span class="mx-2">/</span>
        <span class="font-medium text-gray-900"><?php echo e($church->name); ?></span>
    </li>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('page-actions'); ?>
    <div class="flex items-center space-x-3">
        <!-- Language Switcher -->
        <?php if (isset($component)) { $__componentOriginal8d3bff7d7383a45350f7495fc470d934 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal8d3bff7d7383a45350f7495fc470d934 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.language-switcher','data' => ['position' => 'bottom-right','size' => 'normal']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('language-switcher'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['position' => 'bottom-right','size' => 'normal']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal8d3bff7d7383a45350f7495fc470d934)): ?>
<?php $attributes = $__attributesOriginal8d3bff7d7383a45350f7495fc470d934; ?>
<?php unset($__attributesOriginal8d3bff7d7383a45350f7495fc470d934); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal8d3bff7d7383a45350f7495fc470d934)): ?>
<?php $component = $__componentOriginal8d3bff7d7383a45350f7495fc470d934; ?>
<?php unset($__componentOriginal8d3bff7d7383a45350f7495fc470d934); ?>
<?php endif; ?>

        <a href="<?php echo e(route('churches.edit', $church)); ?>"
           class="inline-flex items-center px-4 py-2 border border-transparent rounded-lg text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
            <i class="fas fa-edit mr-2"></i>
            <?php echo e(__('common.edit')); ?>

        </a>
        <a href="<?php echo e(route('churches.index')); ?>"
           class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
            <i class="fas fa-arrow-left mr-2"></i>
            <?php echo e(__('common.back')); ?>

        </a>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <div class="max-w-7xl mx-auto space-y-6">
        <!-- Quick Language Switcher -->
        <div class="flex justify-end">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-2">
                <div class="flex items-center space-x-2 text-sm">
                    <i class="fas fa-globe text-gray-400"></i>
                    <span class="text-gray-600"><?php echo e(__('common.language')); ?>:</span>
                    <div class="flex space-x-1">
                        <?php $__currentLoopData = config('app.locale_names'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $locale => $info): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <button onclick="switchLanguage('<?php echo e($locale); ?>')"
                                    class="px-2 py-1 rounded text-xs font-medium transition-colors duration-200 <?php echo e(app()->getLocale() === $locale ? 'bg-blue-100 text-blue-700' : 'text-gray-600 hover:bg-gray-100'); ?>">
                                <?php echo e($info['flag']); ?> <?php echo e($info['native']); ?>

                            </button>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            </div>
        </div>
        <!-- Church Header -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
            <div class="bg-gradient-to-r from-green-600 to-green-700 px-6 py-8">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-3xl font-bold text-white"><?php echo e($church->name); ?></h1>
                        <div class="flex items-center mt-2 space-x-4">
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                                <i class="fas fa-layer-group text-xs mr-2"></i>
                                <?php echo e(__('common.' . strtolower($church->level->value))); ?>

                            </span>
                            <span class="text-green-100 flex items-center">
                                <i class="fas fa-map-marker-alt mr-2"></i>
                                <?php echo e($church->location); ?>

                            </span>
                            <?php if($church->date_established): ?>
                                <span class="text-green-100 flex items-center">
                                    <i class="fas fa-calendar mr-2"></i>
                                    <?php echo e(__('churches.established')); ?> <?php echo e($church->date_established->format('Y')); ?>

                                </span>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="text-right">
                        <div class="text-green-100 text-sm"><?php echo e(__('churches.church_id')); ?></div>
                        <div class="text-white text-2xl font-bold">#<?php echo e($church->id); ?></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-users text-blue-600 text-xl"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600"><?php echo e(__('churches.total_members')); ?></p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo e($stats['total_users']); ?></p>
                        <p class="text-xs text-gray-500"><?php echo e($stats['active_users']); ?> <?php echo e(__('common.active')); ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-crown text-green-600 text-xl"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600"><?php echo e(__('churches.leaders')); ?></p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo e($stats['total_leaders']); ?></p>
                        <p class="text-xs text-gray-500"><?php echo e(__('churches.leadership_positions')); ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-sitemap text-purple-600 text-xl"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600"><?php echo e(__('churches.child_churches')); ?></p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo e($stats['child_churches']); ?></p>
                        <p class="text-xs text-gray-500"><?php echo e(__('churches.under_supervision')); ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-calendar-alt text-orange-600 text-xl"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600"><?php echo e(__('churches.established')); ?></p>
                        <p class="text-lg font-bold text-gray-900">
                            <?php echo e($church->date_established ? $church->date_established->format('M Y') : __('common.unknown')); ?>

                        </p>
                        <p class="text-xs text-gray-500">
                            <?php echo e($church->date_established ? $church->date_established->diffForHumans() : ''); ?>

                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Church Information -->
            <div class="lg:col-span-2 space-y-6">
                <!-- Basic Information -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                            <i class="fas fa-info-circle mr-2 text-blue-600"></i>
                            <?php echo e(__('churches.basic_information')); ?>

                        </h3>
                    </div>
                    <div class="p-6">
                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-600"><?php echo e(__('churches.church_name')); ?></label>
                                <p class="mt-1 text-lg font-semibold text-gray-900"><?php echo e($church->name); ?></p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-600"><?php echo e(__('churches.church_level')); ?></label>
                                <p class="mt-1 text-lg text-gray-900"><?php echo e(__('common.' . strtolower($church->level->value))); ?></p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-600"><?php echo e(__('churches.location')); ?></label>
                                <p class="mt-1 text-lg text-gray-900"><?php echo e($church->location); ?></p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-600"><?php echo e(__('churches.date_established')); ?></label>
                                <p class="mt-1 text-lg text-gray-900">
                                    <?php echo e($church->date_established ? $church->date_established->format('F d, Y') : __('common.not_specified')); ?>

                                </p>
                            </div>
                        </div>

                        <!-- Contact Information Section -->
                        <div class="mt-8 pt-6 border-t border-gray-200">
                            <h4 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                                <i class="fas fa-address-book mr-2 text-green-600"></i>
                                <?php echo e(__('common.contact_information')); ?>

                            </h4>
                            <div class="grid grid-cols-1 sm:grid-cols-2 gap-6">
                                <?php if($church->phone_number): ?>
                                <div>
                                    <label class="block text-sm font-medium text-gray-600"><?php echo e(__('churches.phone_number')); ?></label>
                                    <p class="mt-1 text-lg text-gray-900 flex items-center">
                                        <i class="fas fa-phone mr-2 text-blue-500"></i>
                                        <a href="tel:<?php echo e($church->phone_number); ?>" class="hover:text-blue-600"><?php echo e($church->phone_number); ?></a>
                                    </p>
                                </div>
                                <?php endif; ?>

                                <?php if($church->email): ?>
                                <div>
                                    <label class="block text-sm font-medium text-gray-600"><?php echo e(__('churches.email')); ?></label>
                                    <p class="mt-1 text-lg text-gray-900 flex items-center">
                                        <i class="fas fa-envelope mr-2 text-green-500"></i>
                                        <a href="mailto:<?php echo e($church->email); ?>" class="hover:text-green-600"><?php echo e($church->email); ?></a>
                                    </p>
                                </div>
                                <?php endif; ?>

                                <?php if($church->district): ?>
                                <div>
                                    <label class="block text-sm font-medium text-gray-600"><?php echo e(__('churches.district')); ?></label>
                                    <p class="mt-1 text-lg text-gray-900 flex items-center">
                                        <i class="fas fa-map mr-2 text-purple-500"></i>
                                        <?php echo e($church->district); ?>

                                    </p>
                                </div>
                                <?php endif; ?>

                                <?php if($church->region): ?>
                                <div>
                                    <label class="block text-sm font-medium text-gray-600"><?php echo e(__('churches.region')); ?></label>
                                    <p class="mt-1 text-lg text-gray-900 flex items-center">
                                        <i class="fas fa-globe-africa mr-2 text-orange-500"></i>
                                        <?php echo e($church->region); ?>

                                    </p>
                                </div>
                                <?php endif; ?>

                                <?php if($church->address): ?>
                                <div class="sm:col-span-2">
                                    <label class="block text-sm font-medium text-gray-600"><?php echo e(__('churches.address')); ?></label>
                                    <p class="mt-1 text-lg text-gray-900 flex items-start">
                                        <i class="fas fa-map-marked-alt mr-2 text-red-500 mt-1"></i>
                                        <span class="whitespace-pre-line"><?php echo e($church->address); ?></span>
                                    </p>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <?php if($church->parentChurch): ?>
                            <div class="mt-6 pt-6 border-t border-gray-200">
                                <label class="block text-sm font-medium text-gray-600"><?php echo e(__('churches.parent_church')); ?></label>
                                <div class="mt-2 flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                                    <div>
                                        <p class="font-medium text-gray-900"><?php echo e($church->parentChurch->name); ?></p>
                                        <p class="text-sm text-gray-600"><?php echo e(__('common.' . strtolower($church->parentChurch->level->value))); ?> - <?php echo e($church->parentChurch->location); ?></p>
                                    </div>
                                    <a href="<?php echo e(route('churches.show', $church->parentChurch)); ?>"
                                       class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                        <?php echo e(__('common.view')); ?> →
                                    </a>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Leaders Section -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                            <i class="fas fa-crown mr-2 text-green-600"></i>
                            <?php echo e(__('churches.church_leaders')); ?>

                            <span class="ml-2 bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                                <?php echo e($leaders->count()); ?>

                            </span>
                        </h3>
                    </div>
                    <div class="p-6">
                        <?php if($leaders->count() > 0): ?>
                            <div class="space-y-4">
                                <?php $__currentLoopData = $leaders; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $leader): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0">
                                                <?php if($leader->user->profile_picture): ?>
                                                    <img class="h-12 w-12 rounded-full object-cover"
                                                         src="<?php echo e(asset('storage/' . $leader->user->profile_picture)); ?>"
                                                         alt="<?php echo e($leader->user->full_name); ?>">
                                                <?php else: ?>
                                                    <div class="h-12 w-12 bg-green-100 rounded-full flex items-center justify-center">
                                                        <span class="text-sm font-medium text-green-600">
                                                            <?php echo e(strtoupper(substr($leader->user->full_name, 0, 1))); ?>

                                                        </span>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                            <div class="ml-4">
                                                <p class="text-sm font-medium text-gray-900"><?php echo e($leader->user->full_name); ?></p>
                                                <p class="text-sm text-gray-600"><?php echo e($leader->role); ?></p>
                                                <p class="text-xs text-gray-500"><?php echo e($leader->user->email); ?></p>
                                                <?php if($leader->user->phone_number): ?>
                                                <p class="text-xs text-gray-500">
                                                    <i class="fas fa-phone mr-1"></i>
                                                    <?php echo e($leader->user->phone_number); ?>

                                                </p>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                        <div class="flex items-center space-x-2">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                <?php echo e(__('churches.leader')); ?>

                                            </span>
                                            <a href="<?php echo e(route('users.show', $leader->user)); ?>"
                                               class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                                <?php echo e(__('common.view')); ?>

                                            </a>
                                        </div>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        <?php else: ?>
                            <div class="text-center py-8">
                                <i class="fas fa-crown text-gray-300 text-4xl mb-4"></i>
                                <p class="text-gray-500 text-lg"><?php echo e(__('churches.no_leaders_assigned')); ?></p>
                                <p class="text-gray-400 text-sm"><?php echo e(__('churches.leaders_auto_assigned')); ?></p>
                                <p class="text-gray-400 text-xs mt-2"><?php echo e(__('churches.leaders_based_on_roles')); ?></p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Church Members -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                            <i class="fas fa-users mr-2 text-blue-600"></i>
                            <?php echo e(__('churches.church_members')); ?>

                            <span class="ml-2 bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                                <?php echo e($church->users->count()); ?>

                            </span>
                        </h3>
                    </div>
                    <div class="p-6">
                        <?php if($church->users->count() > 0): ?>
                            <div class="space-y-3 max-h-96 overflow-y-auto">
                                <?php $__currentLoopData = $church->users->take(10); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0">
                                                <?php if($user->profile_picture): ?>
                                                    <img class="h-8 w-8 rounded-full object-cover"
                                                         src="<?php echo e(asset('storage/' . $user->profile_picture)); ?>"
                                                         alt="<?php echo e($user->full_name); ?>">
                                                <?php else: ?>
                                                    <div class="h-8 w-8 bg-blue-100 rounded-full flex items-center justify-center">
                                                        <span class="text-xs font-medium text-blue-600">
                                                            <?php echo e(strtoupper(substr($user->full_name, 0, 1))); ?>

                                                        </span>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                            <div class="ml-3">
                                                <p class="text-sm font-medium text-gray-900"><?php echo e($user->full_name); ?></p>
                                                <p class="text-xs text-gray-500"><?php echo e($user->role); ?></p>
                                            </div>
                                        </div>
                                        <a href="<?php echo e(route('users.show', $user)); ?>"
                                           class="text-blue-600 hover:text-blue-800 text-xs font-medium">
                                            <?php echo e(__('common.view')); ?>

                                        </a>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php if($church->users->count() > 10): ?>
                                    <div class="text-center pt-3 border-t border-gray-200">
                                        <p class="text-sm text-gray-500">
                                            <?php echo e(__('churches.and_more_members', ['count' => $church->users->count() - 10])); ?>

                                        </p>
                                    </div>
                                <?php endif; ?>
                            </div>
                        <?php else: ?>
                            <div class="text-center py-6">
                                <i class="fas fa-users text-gray-300 text-3xl mb-3"></i>
                                <p class="text-gray-500"><?php echo e(__('churches.no_members')); ?></p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Child Churches -->
                <?php if($church->childChurches->count() > 0): ?>
                    <div class="bg-white rounded-xl shadow-sm border border-gray-200">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                                <i class="fas fa-sitemap mr-2 text-purple-600"></i>
                                <?php echo e(__('churches.child_churches')); ?>

                                <span class="ml-2 bg-purple-100 text-purple-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                                    <?php echo e($church->childChurches->count()); ?>

                                </span>
                            </h3>
                        </div>
                        <div class="p-6">
                            <div class="space-y-3">
                                <?php $__currentLoopData = $church->childChurches; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $child): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                                        <div>
                                            <p class="text-sm font-medium text-gray-900"><?php echo e($child->name); ?></p>
                                            <p class="text-xs text-gray-500"><?php echo e(__('common.' . strtolower($child->level->value))); ?> - <?php echo e($child->location); ?></p>
                                        </div>
                                        <a href="<?php echo e(route('churches.show', $child)); ?>"
                                           class="text-purple-600 hover:text-purple-800 text-sm font-medium">
                                            <?php echo e(__('common.view')); ?> →
                                        </a>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\wamp64\www\fpct-system\resources\views/churches/show.blade.php ENDPATH**/ ?>