<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\Church;
use App\Models\NotificationPreference;
use App\Enums\ChurchLevel;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Role;

class SuperAdminSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // First, create the National Headquarters church if it doesn't exist
        $nationalChurch = Church::firstOrCreate(
            ['level' => ChurchLevel::NATIONAL],
            [
                'name' => 'FPCT National Headquarters',
                'level' => ChurchLevel::NATIONAL,
                'location' => 'Dar es Salaam, Tanzania',
                'date_established' => now()->subYears(50), // Assuming FPCT was established 50 years ago
                'parent_church_id' => null,
            ]
        );

        $this->command->info("✓ National Headquarters church created/found: {$nationalChurch->name}");

        // Create the super admin user
        $superAdmin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'full_name' => 'FPCT Church Management System (CMS) Administrator',
                'email' => '<EMAIL>',
                'phone_number' => '+255712345678',
                'password' => Hash::make('admin123'),
                'church_id' => $nationalChurch->id,
                'role' => 'Archbishop',
                'is_active' => true,
                'is_first_login' => false, // Set to false so admin can login immediately
                'email_verified_at' => now(),
            ]
        );

        $this->command->info("✓ Super admin user created/found: {$superAdmin->email}");

        // Assign the Archbishop role (which has all permissions)
        $archbishopRole = Role::where('name', 'Archbishop')->first();
        if ($archbishopRole) {
            $superAdmin->assignRole($archbishopRole);
            $this->command->info("✓ Archbishop role assigned to super admin");
        } else {
            $this->command->warn("⚠ Archbishop role not found. Please run RolesAndPermissionsSeeder first.");
        }

        // Create notification preferences for the super admin
        NotificationPreference::firstOrCreate(
            ['user_id' => $superAdmin->id],
            NotificationPreference::getDefaultPreferences()
        );

        $this->command->info("✓ Notification preferences created for super admin");

        // Create a few additional test users for demonstration
        $this->createTestUsers($nationalChurch);

        $this->command->info("\n🎉 Super Admin Setup Complete!");
        $this->command->info("📧 Email: <EMAIL>");
        $this->command->info("🔑 Password: admin123");
        $this->command->info("🏢 Church: {$nationalChurch->name}");
        $this->command->info("👑 Role: Archbishop (Full System Access)");
        $this->command->info("\n⚠️  IMPORTANT: Change the password after first login!");
    }

    private function createTestUsers(Church $nationalChurch): void
    {
        // Create a National Assistant
        $nationalAssistant = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'full_name' => 'National Assistant',
                'email' => '<EMAIL>',
                'phone_number' => '+255712345679',
                'password' => Hash::make('assistant123'),
                'church_id' => $nationalChurch->id,
                'role' => 'National Assistant',
                'is_active' => true,
                'is_first_login' => true, // Will need to change password on first login
                'email_verified_at' => now(),
            ]
        );

        $assistantRole = Role::where('name', 'National Assistant')->first();
        if ($assistantRole) {
            $nationalAssistant->assignRole($assistantRole);
        }

        NotificationPreference::firstOrCreate(
            ['user_id' => $nationalAssistant->id],
            NotificationPreference::getDefaultPreferences()
        );

        // Create a General Secretary
        $secretary = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'full_name' => 'General Secretary',
                'email' => '<EMAIL>',
                'phone_number' => '+255712345680',
                'password' => Hash::make('secretary123'),
                'church_id' => $nationalChurch->id,
                'role' => 'General Secretary',
                'is_active' => true,
                'is_first_login' => true,
                'email_verified_at' => now(),
            ]
        );

        $secretaryRole = Role::where('name', 'General Secretary')->first();
        if ($secretaryRole) {
            $secretary->assignRole($secretaryRole);
        }

        NotificationPreference::firstOrCreate(
            ['user_id' => $secretary->id],
            NotificationPreference::getDefaultPreferences()
        );

        $this->command->info("✓ Test users created:");
        $this->command->info("  - National Assistant: <EMAIL> / assistant123");
        $this->command->info("  - General Secretary: <EMAIL> / secretary123");
    }
}
