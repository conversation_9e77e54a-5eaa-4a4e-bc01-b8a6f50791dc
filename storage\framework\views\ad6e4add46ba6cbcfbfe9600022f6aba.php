

<?php $__env->startSection('title', 'Create New User'); ?>
<?php $__env->startSection('page-title', 'Create New User'); ?>

<?php $__env->startSection('breadcrumbs'); ?>
    <li>
        <span class="mx-2">/</span>
        <a href="<?php echo e(route('users.index')); ?>" class="hover:text-gray-700">Users</a>
    </li>
    <li>
        <span class="mx-2">/</span>
        <span class="font-medium text-gray-900">Create</span>
    </li>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('page-actions'); ?>
    <a href="<?php echo e(route('users.index')); ?>"
       class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
        <i class="fas fa-arrow-left mr-2"></i>
        Back to Users
    </a>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="max-w-2xl mx-auto">
    <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">User Information</h3>
            <p class="mt-1 text-sm text-gray-600">Create a new user account. The user will complete their profile details on first login.</p>
        </div>

        <form method="POST" action="<?php echo e(route('users.store')); ?>" class="p-6 space-y-6">
            <?php echo csrf_field(); ?>

            <!-- Email -->
            <div>
                <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                    Email Address <span class="text-red-500">*</span>
                </label>
                <input type="email"
                       id="email"
                       name="email"
                       value="<?php echo e(old('email')); ?>"
                       class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-300 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                       placeholder="Enter email address">
                <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>

            <!-- Phone Number -->
            <div>
                <label for="phone_number" class="block text-sm font-medium text-gray-700 mb-2">
                    Phone Number <span class="text-red-500">*</span>
                </label>
                <input type="tel"
                       id="phone_number"
                       name="phone_number"
                       value="<?php echo e(old('phone_number')); ?>"
                       class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 <?php $__errorArgs = ['phone_number'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-300 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                       placeholder="Enter phone number (e.g., 255787504956)">
                <?php $__errorArgs = ['phone_number'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>

            <!-- Church Assignment -->
            <div>
                <label for="church_id" class="block text-sm font-medium text-gray-700 mb-2">
                    Church Assignment <span class="text-red-500">*</span>
                </label>
                <select id="church_id"
                        name="church_id"
                        required
                        onchange="filterRolesByChurch()"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 <?php $__errorArgs = ['church_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-300 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                    <option value="">Select a church</option>
                    <?php $__currentLoopData = $churches; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $church): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($church->id); ?>"
                                data-level="<?php echo e($church->level->value); ?>"
                                <?php echo e(old('church_id') == $church->id ? 'selected' : ''); ?>>
                            <?php echo e($church->name); ?> (<?php echo e($church->level->value); ?>)
                        </option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
                <?php $__errorArgs = ['church_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>

            <!-- Role Assignment -->
            <div>
                <label for="role" class="block text-sm font-medium text-gray-700 mb-2">
                    Role Assignment <span class="text-red-500">*</span>
                </label>
                <select id="role"
                        name="role"
                        required
                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 <?php $__errorArgs = ['role'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-300 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                    <option value="">Select a role</option>
                    <?php $__currentLoopData = $roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($role->name); ?>"
                                data-level="<?php echo e($role->level ?? 'all'); ?>"
                                <?php echo e(old('role') == $role->name ? 'selected' : ''); ?>>
                            <?php echo e($role->name); ?>

                        </option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
                <?php $__errorArgs = ['role'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>

            <!-- Info Box -->
            <div class="bg-blue-50 border border-blue-200 rounded-md p-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <i class="fas fa-info-circle text-blue-400"></i>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-blue-800">
                            Account Creation Process
                        </h3>
                        <div class="mt-2 text-sm text-blue-700">
                            <p><strong>What happens after creating the user:</strong></p>
                            <ul class="list-disc list-inside mt-1 space-y-1">
                                <li>A temporary password will be generated automatically</li>
                                <li>Login credentials (email, password, and OTP) will be sent via email and SMS</li>
                                <li>User must verify OTP on first login</li>
                                <li>User will be prompted to complete their profile and change password</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Submit Button -->
            <div class="flex justify-end space-x-3 pt-6 border-t">
                <a href="<?php echo e(route('users.index')); ?>"
                   class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    Cancel
                </a>
                <button type="submit"
                        class="px-4 py-2 bg-blue-600 border border-transparent rounded-md text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    Create User
                </button>
            </div>
        </form>

<script>
    // Role mapping for each church level
    const levelRoles = {
        'National': ['Archbishop', 'General Secretary', 'National Treasurer', 'National IT', 'National HR'],
        'Diocese': ['Bishop', 'Diocese Secretary', 'Diocese Treasurer', 'Diocese IT'],
        'Local': ['Pastor', 'Local Secretary', 'Local Treasurer', 'Local IT'],
        'Parish': ['Parish Pastor', 'Parish Secretary', 'Parish Treasurer', 'Parish IT'],
        'Branch': ['Branch Pastor', 'Branch Secretary', 'Branch Treasurer', 'Branch IT']
    };

    function filterRolesByChurch() {
        const churchSelect = document.getElementById('church_id');
        const roleSelect = document.getElementById('role');
        const selectedOption = churchSelect.options[churchSelect.selectedIndex];

        if (!selectedOption || !selectedOption.value) {
            // Show all roles if no church selected
            Array.from(roleSelect.options).forEach(option => {
                if (option.value !== '') {
                    option.style.display = 'block';
                }
            });
            return;
        }

        const churchLevel = selectedOption.getAttribute('data-level');
        const allowedRoles = levelRoles[churchLevel] || [];

        // Hide/show role options based on church level
        Array.from(roleSelect.options).forEach(option => {
            if (option.value === '') {
                option.style.display = 'block'; // Always show "Select a role" option
                return;
            }

            if (allowedRoles.includes(option.value)) {
                option.style.display = 'block';
            } else {
                option.style.display = 'none';
                // Clear selection if currently selected role is not allowed
                if (option.selected) {
                    roleSelect.value = '';
                }
            }
        });
    }

    // Filter roles on page load if church is already selected
    document.addEventListener('DOMContentLoaded', function() {
        filterRolesByChurch();
    });
</script>

<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\wamp64\www\fpct-system\resources\views/users/create.blade.php ENDPATH**/ ?>