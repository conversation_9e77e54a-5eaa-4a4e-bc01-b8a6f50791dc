

<?php $__env->startSection('title', __('requests.index_title')); ?>
<?php $__env->startSection('page-title', __('requests.requests')); ?>

<?php $__env->startSection('breadcrumbs'); ?>
    <li>
        <span class="mx-2">/</span>
        <span class="font-medium text-gray-900"><?php echo e(__('requests.requests')); ?></span>
    </li>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="space-y-6">
    <!-- Success/Error Messages -->
    <?php if(session('success')): ?>
        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
            <div class="flex">
                <div class="flex-shrink-0">
                    <i class="fas fa-check-circle text-green-400"></i>
                </div>
                <div class="ml-3">
                    <p class="text-sm"><?php echo e(session('success')); ?></p>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <?php if(session('error')): ?>
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
            <div class="flex">
                <div class="flex-shrink-0">
                    <i class="fas fa-exclamation-circle text-red-400"></i>
                </div>
                <div class="ml-3">
                    <p class="text-sm"><?php echo e(session('error')); ?></p>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <!-- Header -->
    <div class="md:flex md:items-center md:justify-between">
        <div class="flex-1 min-w-0">
            <h2 class="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
                <i class="fas fa-file-alt mr-2 text-blue-600"></i>
                <?php echo e(__('requests.request_management')); ?>

            </h2>
            <p class="mt-1 text-sm text-gray-500">
                <?php echo e(__('requests.manage_requests_system')); ?>

            </p>
        </div>
        <div class="mt-4 flex md:mt-0 md:ml-4">
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create-requests')): ?>
            <a href="<?php echo e(route('requests.create')); ?>"
               class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                <i class="fas fa-plus mr-2"></i>
                New Request
            </a>
            <?php else: ?>
            <div class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-500 bg-gray-100 cursor-not-allowed" title="You don't have permission to create requests">
                <i class="fas fa-lock mr-2"></i>
                New Request
            </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Request Tabs -->
    <div class="border-b border-gray-200">
        <nav class="-mb-px flex space-x-8">
            <a href="<?php echo e(route('requests.index')); ?>"
               class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm
                      <?php echo e(!request()->has('filter') ? 'border-blue-500 text-blue-600' : ''); ?>">
                All Requests
            </a>
            <a href="<?php echo e(route('requests.index')); ?>?filter=pending"
               class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm
                      <?php echo e(request('filter') == 'pending' ? 'border-blue-500 text-blue-600' : ''); ?>">
                Pending
                <?php if($pendingCount ?? 0 > 0): ?>
                    <span class="bg-yellow-100 text-yellow-800 text-xs font-medium ml-2 px-2.5 py-0.5 rounded-full">
                        <?php echo e($pendingCount); ?>

                    </span>
                <?php endif; ?>
            </a>
            <a href="<?php echo e(route('requests.index')); ?>?filter=my_requests"
               class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm
                      <?php echo e(request('filter') == 'my_requests' ? 'border-blue-500 text-blue-600' : ''); ?>">
                My Requests
            </a>
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('approve-requests')): ?>
            <a href="<?php echo e(route('requests.index')); ?>?filter=awaiting_approval"
               class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm
                      <?php echo e(request('filter') == 'awaiting_approval' ? 'border-blue-500 text-blue-600' : ''); ?>">
                Awaiting My Approval
                <?php if($awaitingApprovalCount ?? 0 > 0): ?>
                    <span class="bg-red-100 text-red-800 text-xs font-medium ml-2 px-2.5 py-0.5 rounded-full">
                        <?php echo e($awaitingApprovalCount); ?>

                    </span>
                <?php endif; ?>
            </a>
            <?php endif; ?>
        </nav>
    </div>

    <!-- Filters and Search -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <form method="GET" action="<?php echo e(route('requests.index')); ?>" class="space-y-4 md:space-y-0 md:flex md:items-end md:space-x-4">
                <input type="hidden" name="filter" value="<?php echo e(request('filter')); ?>">

                <!-- Search -->
                <div class="flex-1">
                    <label for="search" class="block text-sm font-medium text-gray-700">Search</label>
                    <div class="mt-1 relative rounded-md shadow-sm">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-search text-gray-400"></i>
                        </div>
                        <input type="text"
                               name="search"
                               id="search"
                               value="<?php echo e(request('search')); ?>"
                               placeholder="Search requests..."
                               class="focus:ring-blue-500 focus:border-blue-500 block w-full pl-10 sm:text-sm border-gray-300 rounded-md">
                    </div>
                </div>

                <!-- Status Filter -->
                <div class="min-w-0 flex-1 md:max-w-xs">
                    <label for="status" class="block text-sm font-medium text-gray-700">Status</label>
                    <select name="status"
                            id="status"
                            class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md">
                        <option value="">All Statuses</option>
                        <option value="pending" <?php echo e(request('status') == 'pending' ? 'selected' : ''); ?>>Pending</option>
                        <option value="approved" <?php echo e(request('status') == 'approved' ? 'selected' : ''); ?>>Approved</option>
                        <option value="rejected" <?php echo e(request('status') == 'rejected' ? 'selected' : ''); ?>>Rejected</option>
                    </select>
                </div>

                <!-- Type Filter -->
                <div class="min-w-0 flex-1 md:max-w-xs">
                    <label for="type" class="block text-sm font-medium text-gray-700">Request Type</label>
                    <select name="type"
                            id="type"
                            class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md">
                        <option value="">All Types</option>
                        <?php $__currentLoopData = $requestTypes ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $type): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($type); ?>" <?php echo e(request('type') == $type ? 'selected' : ''); ?>>
                                <?php echo e(ucwords(str_replace('_', ' ', $type))); ?>

                            </option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>

                <!-- Buttons -->
                <div class="flex space-x-2">
                    <button type="submit"
                            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <i class="fas fa-search mr-2"></i>
                        Filter
                    </button>
                    <a href="<?php echo e(route('requests.index')); ?>"
                       class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <i class="fas fa-times mr-2"></i>
                        Clear
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Requests Table -->
    <div class="bg-white shadow overflow-hidden sm:rounded-md">
        <div class="px-4 py-5 sm:px-6 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h3 class="text-lg leading-6 font-medium text-gray-900">
                    <?php if(request('filter') == 'pending'): ?>
                        Pending Requests
                    <?php elseif(request('filter') == 'my_requests'): ?>
                        My Requests
                    <?php elseif(request('filter') == 'awaiting_approval'): ?>
                        Awaiting My Approval
                    <?php else: ?>
                        All Requests
                    <?php endif; ?>
                </h3>
                <p class="text-sm text-gray-500">
                    <?php echo e($requests->total()); ?> requests
                </p>
            </div>
        </div>

        <?php if($requests->count() > 0): ?>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Request Details
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Church & Requester
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Status & Progress
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Date
                        </th>
                        <th scope="col" class="relative px-6 py-3">
                            <span class="sr-only">Actions</span>
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <?php $__currentLoopData = $requests; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $request): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 h-10 w-10">
                                    <div class="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                                        <i class="fas fa-file-alt text-blue-600"></i>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <div class="text-sm font-medium text-gray-900">
                                        <?php echo e(ucwords(str_replace('_', ' ', $request->type))); ?>

                                    </div>
                                    <div class="text-sm text-gray-500">
                                        ID: #<?php echo e($request->id); ?>

                                    </div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">
                                <div class="font-medium"><?php echo e($request->church ? $request->church->name : 'Church not found'); ?></div>
                                <div class="text-gray-500"><?php echo e($request->church ? $request->church->level->value . ' Level' : 'Unknown Level'); ?></div>
                                <div class="text-xs text-gray-400 mt-1">
                                    By: <?php echo e($request->user ? $request->user->full_name : 'Unknown User'); ?>

                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex flex-col space-y-2">
                                <!-- Status Badge -->
                                <?php if($request->status === 'pending'): ?>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                        <i class="fas fa-clock mr-1"></i>
                                        Pending
                                    </span>
                                <?php elseif($request->status === 'approved'): ?>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        <i class="fas fa-check-circle mr-1"></i>
                                        Approved
                                    </span>
                                <?php elseif($request->status === 'rejected'): ?>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                        <i class="fas fa-times-circle mr-1"></i>
                                        Rejected
                                    </span>
                                <?php endif; ?>

                                <!-- Approval Progress -->
                                <?php if($request->approvalWorkflows && $request->approvalWorkflows->count() > 0): ?>
                                <div class="text-xs text-gray-500">
                                    <?php echo e($request->approvalWorkflows->where('status', 'approved')->count()); ?> /
                                    <?php echo e($request->approvalWorkflows->count()); ?> approvals
                                </div>
                                <?php endif; ?>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            <div><?php echo e($request->created_at ? $request->created_at->format('M d, Y') : 'Unknown'); ?></div>
                            <div class="text-xs"><?php echo e($request->created_at ? $request->created_at->diffForHumans() : 'Unknown'); ?></div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <div class="flex items-center space-x-2">
                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-requests')): ?>
                                <a href="<?php echo e(route('requests.show', $request)); ?>"
                                   class="text-blue-600 hover:text-blue-900"
                                   title="View Request">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <?php endif; ?>

                                <?php if($request->status === 'pending'): ?>
                                    <?php if($request->user_id === auth()->id() && auth()->user()->hasPermissionTo('manage-requests')): ?>
                                    <a href="<?php echo e(route('requests.edit', $request)); ?>"
                                       class="text-indigo-600 hover:text-indigo-900"
                                       title="Edit Request">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <?php endif; ?>

                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('approve-requests')): ?>
                                    <?php if($request->canBeApprovedBy(auth()->user())): ?>
                                    <form action="<?php echo e(route('requests.approve', $request)); ?>"
                                          method="POST"
                                          class="inline"
                                          onsubmit="return confirm('Are you sure you want to approve this request?')">
                                        <?php echo csrf_field(); ?>
                                        <button type="submit"
                                                class="text-green-600 hover:text-green-900"
                                                title="Approve Request">
                                            <i class="fas fa-check"></i>
                                        </button>
                                    </form>

                                    <a href="<?php echo e(route('requests.reject', $request)); ?>"
                                       class="text-red-600 hover:text-red-900"
                                       title="Reject Request">
                                        <i class="fas fa-times"></i>
                                    </a>
                                    <?php endif; ?>
                                    <?php endif; ?>

                                    <?php if($request->user_id === auth()->id()): ?>
                                    <form action="<?php echo e(route('requests.destroy', $request)); ?>"
                                          method="POST"
                                          class="inline"
                                          onsubmit="return confirm('Are you sure you want to delete this request?')">
                                        <?php echo csrf_field(); ?>
                                        <?php echo method_field('DELETE'); ?>
                                        <button type="submit"
                                                class="text-red-600 hover:text-red-900"
                                                title="Delete Request">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                    <?php endif; ?>
                                <?php endif; ?>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <div class="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
            <?php echo e($requests->appends(request()->query())->links()); ?>

        </div>
        <?php else: ?>
        <div class="text-center py-12">
            <i class="fas fa-file-alt text-gray-400 text-4xl mb-4"></i>
            <h3 class="text-lg font-medium text-gray-900 mb-2">No requests found</h3>
            <p class="text-gray-500 mb-4">
                <?php if(request()->hasAny(['search', 'status', 'type', 'filter'])): ?>
                    No requests match your current filters.
                <?php else: ?>
                    Get started by creating your first request.
                <?php endif; ?>
            </p>
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create-requests')): ?>
            <a href="<?php echo e(route('requests.create')); ?>"
               class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                <i class="fas fa-plus mr-2"></i>
                Create Request
            </a>
            <?php endif; ?>
        </div>
        <?php endif; ?>
    </div>
</div>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\wamp64\www\fpct-system\resources\views/requests/index.blade.php ENDPATH**/ ?>