<?php

namespace App\Http\Controllers;

use App\Models\Church;
use App\Models\ChurchLeader;
use App\Models\User;
use App\Services\ChurchHierarchyService;
use App\Enums\ChurchLevel;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Spatie\Permission\Models\Role;

class ChurchController extends Controller
{
    protected $hierarchyService;

    public function __construct(ChurchHierarchyService $hierarchyService)
    {
        $this->hierarchyService = $hierarchyService;
        $this->middleware('auth');
        $this->middleware('permission:manage-churches', ['only' => ['index', 'create', 'store', 'edit', 'update', 'destroy', 'trashed', 'restore', 'forceDelete']]);
    }

    // List all churches
    public function index(Request $request)
    {
        $user = Auth::user();

        // Get churches user can access based on hierarchy
        $accessibleChurches = $this->hierarchyService->getChurchesUserCanAccess($user);
        $accessibleChurchIds = $accessibleChurches->pluck('id')->toArray();

        $churches = Church::with('parentChurch')
            ->withCount(['users', 'childChurches as children_count'])
            ->whereIn('id', $accessibleChurchIds)
            ->when($request->search, function ($query, $search) {
                return $query->where('name', 'like', "%{$search}%")
                            ->orWhere('location', 'like', "%{$search}%")
                            ->orWhere('level', 'like', "%{$search}%")
                            ->orWhere('phone_number', 'like', "%{$search}%")
                            ->orWhere('email', 'like', "%{$search}%")
                            ->orWhere('address', 'like', "%{$search}%")
                            ->orWhere('district', 'like', "%{$search}%")
                            ->orWhere('region', 'like', "%{$search}%");
            })
            ->when($request->level, function ($query, $level) {
                return $query->where('level', $level);
            })
            ->withTrashed()
            ->paginate(10);

        return view('churches.index', compact('churches'));
    }

    // Show create church form
    public function create()
    {
        $parentChurches = collect(); // Start with empty collection, will be populated via AJAX
        $roles = Role::all();
        $levels = ['National', 'Regional', 'Local', 'Parish', 'Branch'];
        return view('churches.create', compact('parentChurches', 'roles', 'levels'));
    }

    // Get parent churches based on selected level (AJAX endpoint)
    public function getParentChurches(Request $request)
    {
        $level = $request->get('level');

        if (!$level) {
            return response()->json([]);
        }

        try {
            $levelEnum = ChurchLevel::tryFrom($level);
            if (!$levelEnum) {
                return response()->json([]);
            }

            // National level has no parent
            if ($levelEnum === ChurchLevel::NATIONAL) {
                return response()->json([]);
            }

            // Get the required parent level (one level above)
            $parentLevel = $this->getParentLevel($levelEnum);
            if (!$parentLevel) {
                return response()->json([]);
            }

            // Get churches at the parent level that user can access
            $user = Auth::user();
            $hierarchyService = app(ChurchHierarchyService::class);
            $accessibleChurches = $hierarchyService->getChurchesUserCanAccess($user);

            $parentChurches = $accessibleChurches->where('level', $parentLevel)
                ->map(function ($church) {
                    return [
                        'id' => $church->id,
                        'name' => $church->name,
                        'level' => $church->level->value,
                        'location' => $church->location
                    ];
                })->values();

            return response()->json($parentChurches);

        } catch (\Exception $e) {
            Log::error('Error fetching parent churches: ' . $e->getMessage());
            return response()->json([]);
        }
    }

    // Helper method to get parent level
    private function getParentLevel(ChurchLevel $level): ?ChurchLevel
    {
        return match ($level) {
            ChurchLevel::REGIONAL => ChurchLevel::NATIONAL,
            ChurchLevel::LOCAL => ChurchLevel::REGIONAL,
            ChurchLevel::PARISH => ChurchLevel::LOCAL,
            ChurchLevel::BRANCH => ChurchLevel::PARISH,
            ChurchLevel::NATIONAL => null,
        };
    }

    // Store new church
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'level' => 'required|in:National,Regional,Local,Parish,Branch',
            'location' => 'required|string|max:255',
            'phone_number' => 'nullable|string|max:20',
            'address' => 'nullable|string',
            'email' => 'nullable|email|max:255',
            'district' => 'nullable|string|max:255',
            'region' => 'nullable|string|max:255',
            'date_established' => 'nullable|date',
            'parent_church_id' => 'nullable|exists:churches,id',
            'youth_count' => 'nullable|integer|min:0',
            'young_adults_count' => 'nullable|integer|min:0',
            'children_count' => 'nullable|integer|min:0',
            'elders_count' => 'nullable|integer|min:0',
        ]);

        $church = Church::create([
            'name' => $request->name,
            'level' => $request->level,
            'location' => $request->location,
            'phone_number' => $request->phone_number,
            'address' => $request->address,
            'email' => $request->email,
            'district' => $request->district,
            'region' => $request->region,
            'date_established' => $request->date_established,
            'parent_church_id' => $request->parent_church_id,
            'youth_count' => $request->youth_count ?? 0,
            'young_adults_count' => $request->young_adults_count ?? 0,
            'children_count' => $request->children_count ?? 0,
            'elders_count' => $request->elders_count ?? 0,
        ]);

        return redirect()->route('churches.index')->with('success', 'Church created successfully.');
    }

    // Show church details
    public function show(Church $church)
    {
        $user = Auth::user();

        // Check if user can access this church
        $accessibleChurches = $this->hierarchyService->getChurchesUserCanAccess($user);
        if (!$accessibleChurches->contains('id', $church->id)) {
            abort(403, 'You do not have permission to view this church.');
        }

        $church->load([
            'parentChurch',
            'childChurches',
            'users' => function ($query) {
                $query->where('is_active', true)->orderBy('full_name');
            }
        ]);

        // Get leaders from user roles instead of separate leaders table
        $leaders = $church->getLeadersFromRoles();

        // Get statistics
        $stats = [
            'total_users' => $church->users()->count(),
            'active_users' => $church->users()->where('is_active', true)->count(),
            'total_leaders' => $leaders->count(),
            'child_churches' => $church->childChurches()->count(),
        ];

        return view('churches.show', compact('church', 'stats', 'leaders'));
    }

    // Show edit church form
    public function edit(Church $church)
    {
        $user = Auth::user();

        // Check if user can manage this church
        $manageableChurches = $this->hierarchyService->getChurchesUserCanManage($user);
        if (!$manageableChurches->contains('id', $church->id)) {
            abort(403, 'You do not have permission to edit this church.');
        }

        // Get accessible churches as potential parents (excluding self and descendants)
        $accessibleChurches = $this->hierarchyService->getChurchesUserCanAccess($user);
        $descendants = $church->getAllDescendants();
        $parentChurches = $accessibleChurches->whereNotIn('id',
            $descendants->pluck('id')->push($church->id)
        );

        // Get users from accessible churches for potential leadership assignment
        $accessibleChurchIds = $accessibleChurches->pluck('id')->toArray();
        $churchUsers = User::where('church_id', $church->id)->where('is_active', true)->get();
        $users = User::whereIn('church_id', $accessibleChurchIds)
                    ->where('is_active', true)
                    ->orderBy('full_name')
                    ->get();

        $roles = Role::all();
        $levels = ['National', 'Regional', 'Local', 'Parish', 'Branch'];
        $currentLeaders = $church->leaders->keyBy('user_id');

        // Get statistics
        $stats = [
            'total_users' => $church->users()->count(),
            'active_users' => $church->users()->where('is_active', true)->count(),
            'total_leaders' => $church->leaders()->count(),
            'child_churches' => $church->childChurches()->count(),
        ];

        return view('churches.edit', compact('church', 'parentChurches', 'users', 'roles', 'levels', 'currentLeaders', 'churchUsers', 'stats'));
    }

    // Update church
    public function update(Request $request, Church $church)
    {
        $user = Auth::user();

        // Check if user can manage this church
        $manageableChurches = $this->hierarchyService->getChurchesUserCanManage($user);
        if (!$manageableChurches->contains('id', $church->id)) {
            abort(403, 'You do not have permission to edit this church.');
        }

        $request->validate([
            'name' => 'required|string|max:255',
            'level' => 'required|in:National,Regional,Local,Parish,Branch',
            'location' => 'required|string|max:255',
            'phone_number' => 'nullable|string|max:20',
            'address' => 'nullable|string',
            'email' => 'nullable|email|max:255',
            'district' => 'nullable|string|max:255',
            'region' => 'nullable|string|max:255',
            'date_established' => 'nullable|date',
            'parent_church_id' => 'nullable|exists:churches,id',
            'leaders' => 'array',
            'leaders.*.user_id' => 'required_with:leaders|exists:users,id',
            'leaders.*.position' => 'required_with:leaders|string|max:255',
        ]);

        $church->update([
            'name' => $request->name,
            'level' => $request->level,
            'location' => $request->location,
            'phone_number' => $request->phone_number,
            'address' => $request->address,
            'email' => $request->email,
            'district' => $request->district,
            'region' => $request->region,
            'date_established' => $request->date_established,
            'parent_church_id' => $request->parent_church_id,
        ]);

        // Update leaders
        $church->leaders()->delete();
        if ($request->has('leaders')) {
            foreach ($request->leaders as $leader) {
                ChurchLeader::create([
                    'church_id' => $church->id,
                    'user_id' => $leader['user_id'],
                    'position' => $leader['position'],
                ]);
            }
        }

        return redirect()->route('churches.index')->with('success', 'Church updated successfully.');
    }

    // Soft delete church
    public function destroy(Church $church)
    {
        $church->delete();
        return redirect()->route('churches.index')->with('success', 'Church moved to trash.');
    }

    // List trashed churches
    public function trashed(Request $request)
    {
        $churches = Church::onlyTrashed()
            ->when($request->search, function ($query, $search) {
                return $query->where('name', 'like', "%{$search}%")
                            ->orWhere('location', 'like', "%{$search}%")
                            ->orWhere('phone_number', 'like', "%{$search}%")
                            ->orWhere('email', 'like', "%{$search}%")
                            ->orWhere('address', 'like', "%{$search}%")
                            ->orWhere('district', 'like', "%{$search}%")
                            ->orWhere('region', 'like', "%{$search}%");
            })
            ->paginate(10);

        return view('churches.trashed', compact('churches'));
    }

    // Restore soft-deleted church
    public function restore($id)
    {
        $church = Church::onlyTrashed()->findOrFail($id);
        $church->restore();
        return redirect()->route('churches.trashed')->with('success', 'Church restored successfully.');
    }

    // Permanently delete church
    public function forceDelete($id)
    {
        $church = Church::onlyTrashed()->findOrFail($id);
        $church->forceDelete();
        return redirect()->route('churches.trashed')->with('success', 'Church permanently deleted.');
    }

    /**
     * Get hierarchical church data for AJAX requests
     */
    public function getHierarchicalData(Request $request)
    {
        $user = Auth::user();
        $accessibleChurchIds = $this->hierarchyService->getChurchesUserCanAccess($user)->pluck('id');

        $level = $request->get('level');
        $parentId = $request->get('parent_id');

        $query = Church::with(['parentChurch', 'childChurches'])
            ->withCount(['users', 'childChurches as children_count'])
            ->whereIn('id', $accessibleChurchIds);

        if ($level) {
            $query->where('level', $level);
        }

        if ($parentId) {
            $query->where('parent_church_id', $parentId);
        } elseif ($level === 'Regional') {
            // For Regional level, get churches with no parent (National) or Regional churches
            $query->where(function($q) {
                $q->whereNull('parent_church_id')
                  ->orWhere('level', 'Regional');
            });
        }

        $churches = $query->orderBy('name')->get();

        return response()->json([
            'churches' => $churches->map(function ($church) {
                return [
                    'id' => $church->id,
                    'name' => $church->name,
                    'level' => $church->level->value,
                    'location' => $church->location,
                    'users_count' => $church->users_count,
                    'children_count' => $church->children_count,
                    'parent_church' => $church->parentChurch ? [
                        'id' => $church->parentChurch->id,
                        'name' => $church->parentChurch->name,
                        'level' => $church->parentChurch->level->value,
                    ] : null,
                    'has_children' => $church->children_count > 0,
                ];
            })
        ]);
    }

    /**
     * Get regions for hierarchical navigation
     */
    public function getRegions(Request $request)
    {
        $user = Auth::user();
        $accessibleChurchIds = $this->hierarchyService->getChurchesUserCanAccess($user)->pluck('id');

        $regions = Church::where('level', 'Regional')
            ->whereIn('id', $accessibleChurchIds)
            ->withCount(['users', 'childChurches as children_count'])
            ->orderBy('name')
            ->get();

        return response()->json([
            'regions' => $regions->map(function ($church) {
                return [
                    'id' => $church->id,
                    'name' => $church->name,
                    'location' => $church->location,
                    'users_count' => $church->users_count,
                    'children_count' => $church->children_count,
                ];
            })
        ]);
    }
}